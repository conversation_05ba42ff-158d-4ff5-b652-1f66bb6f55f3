// Cloudflare Workers 订阅代理服务
// 支持IP白名单和订阅地址管理

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;
    
    // 获取客户端IP
    const clientIP = request.headers.get('CF-Connecting-IP') || 
                    request.headers.get('X-Forwarded-For') || 
                    request.headers.get('X-Real-IP') || 
                    '127.0.0.1';

    // 路由处理
    if (path === '/admin') {
      return handleAdmin(request, env);
    } else if (path.startsWith('/api/')) {
      return handleAPI(request, env, clientIP);
    } else if (path.startsWith('/proxy/')) {
      return handleProxy(request, env, clientIP);
    } else if (path === '/whitelist-helper') {
      return handleWhitelistHelper(request);
    } else if (path === '/') {
      return new Response('Subscription Proxy Service', { status: 200 });
    }
    
    return new Response('Not Found', { status: 404 });
  }
};

// 处理管理页面
async function handleAdmin(request, env) {
  if (request.method === 'GET') {
    return new Response(getAdminHTML(), {
      headers: { 'Content-Type': 'text/html' }
    });
  }
  
  if (request.method === 'POST') {
    const formData = await request.formData();
    const password = formData.get('password');
    
    if (password !== env.ADMIN_PASSWORD) {
      return new Response('Unauthorized', { status: 401 });
    }
    
    const action = formData.get('action');
    
    if (action === 'add_subscription') {
      const originalUrl = formData.get('original_url');
      const proxyId = generateId();
      
      await env.SUBSCRIPTION_KV.put(`sub:${proxyId}`, originalUrl);
      
      const proxyUrl = `${new URL(request.url).origin}/proxy/${proxyId}`;
      const whitelistUrl = `${new URL(request.url).origin}/api/whitelist/${proxyId}`;
      
      return new Response(JSON.stringify({
        success: true,
        proxy_url: proxyUrl,
        whitelist_url: whitelistUrl,
        proxy_id: proxyId
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    if (action === 'add_ip') {
      const proxyId = formData.get('proxy_id');
      const ip = formData.get('ip');

      // 验证输入
      if (!proxyId || !ip) {
        return new Response(JSON.stringify({
          success: false,
          error: 'Missing proxy_id or ip'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      try {
        const existingIPs = await env.SUBSCRIPTION_KV.get(`whitelist:${proxyId}`);
        const ipList = existingIPs ? JSON.parse(existingIPs) : [];

        if (!ipList.includes(ip)) {
          ipList.push(ip);
          await env.SUBSCRIPTION_KV.put(`whitelist:${proxyId}`, JSON.stringify(ipList));
        }

        return new Response(JSON.stringify({
          success: true,
          message: `IP ${ip} added to whitelist for proxy ${proxyId}`,
          ip: ip,
          proxy_id: proxyId
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (error) {
        return new Response(JSON.stringify({
          success: false,
          error: `Failed to add IP: ${error.message}`
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }
  }
  
  return new Response('Method Not Allowed', { status: 405 });
}

// 处理API请求
async function handleAPI(request, env, clientIP) {
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  
  if (pathParts[2] === 'whitelist' && pathParts[3]) {
    const proxyId = pathParts[3];
    
    if (request.method === 'POST') {
      // 添加当前IP到白名单
      try {
        const existingIPs = await env.SUBSCRIPTION_KV.get(`whitelist:${proxyId}`);
        const ipList = existingIPs ? JSON.parse(existingIPs) : [];

        if (!ipList.includes(clientIP)) {
          ipList.push(clientIP);
          await env.SUBSCRIPTION_KV.put(`whitelist:${proxyId}`, JSON.stringify(ipList));
        }

        return new Response(JSON.stringify({
          success: true,
          message: `IP ${clientIP} added to whitelist for proxy ${proxyId}`,
          ip: clientIP,
          proxy_id: proxyId,
          total_ips: ipList.length
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (error) {
        return new Response(JSON.stringify({
          success: false,
          error: `Failed to add IP to whitelist: ${error.message}`,
          ip: clientIP,
          proxy_id: proxyId
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }
    
    if (request.method === 'GET') {
      // 检查IP是否在白名单中
      try {
        const existingIPs = await env.SUBSCRIPTION_KV.get(`whitelist:${proxyId}`);
        const ipList = existingIPs ? JSON.parse(existingIPs) : [];

        return new Response(JSON.stringify({
          whitelisted: ipList.includes(clientIP),
          ip: clientIP,
          proxy_id: proxyId,
          total_ips: ipList.length,
          all_ips: ipList // 显示所有白名单IP用于调试
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (error) {
        return new Response(JSON.stringify({
          error: `Failed to check whitelist: ${error.message}`,
          ip: clientIP,
          proxy_id: proxyId
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }
  }
  
  return new Response('API endpoint not found', { status: 404 });
}

// 处理代理请求
async function handleProxy(request, env, clientIP) {
  const url = new URL(request.url);
  const proxyId = url.pathname.split('/')[2];
  
  if (!proxyId) {
    return new Response('Invalid proxy ID', { status: 400 });
  }
  
  // 检查IP白名单
  const whitelistData = await env.SUBSCRIPTION_KV.get(`whitelist:${proxyId}`);
  const whitelist = whitelistData ? JSON.parse(whitelistData) : [];
  
  if (!whitelist.includes(clientIP)) {
    return new Response('Access denied: IP not whitelisted', { status: 403 });
  }
  
  // 获取原始订阅地址
  const originalUrl = await env.SUBSCRIPTION_KV.get(`sub:${proxyId}`);
  
  if (!originalUrl) {
    return new Response('Subscription not found', { status: 404 });
  }
  
  try {
    // 代理请求到原始地址
    const response = await fetch(originalUrl, {
      method: request.method,
      headers: request.headers,
      body: request.body
    });
    
    // 返回代理响应
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers
    });
  } catch (error) {
    return new Response('Proxy error: ' + error.message, { status: 500 });
  }
}

// 处理白名单助手页面
async function handleWhitelistHelper(request) {
  return new Response(getWhitelistHelperHTML(), {
    headers: { 'Content-Type': 'text/html' }
  });
}

// 生成随机ID
function generateId() {
  return Math.random().toString(36).substring(2, 15) +
         Math.random().toString(36).substring(2, 15);
}

// 获取管理页面HTML
function getAdminHTML() {
  return `<!DOCTYPE html>
<html>
<head>
    <title>Subscription Proxy Admin</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin: 20px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; margin-bottom: 10px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; cursor: pointer; }
        button:hover { background: #005a87; }
        .result { background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Subscription Proxy Admin</h1>
        
        <h2>Add New Subscription</h2>
        <form id="addSubscriptionForm">
            <div class="form-group">
                <label>Admin Password:</label>
                <input type="password" id="password" required>
            </div>
            <div class="form-group">
                <label>Original Subscription URL:</label>
                <input type="url" id="originalUrl" placeholder="https://wd-red.com/subscribe/djkbm-mji2hi93" required>
            </div>
            <button type="submit">Create Proxy</button>
        </form>
        
        <div id="subscriptionResult"></div>
        
        <h2>Add IP to Whitelist</h2>
        <form id="addIPForm">
            <div class="form-group">
                <label>Admin Password:</label>
                <input type="password" id="ipPassword" required>
            </div>
            <div class="form-group">
                <label>Proxy ID:</label>
                <input type="text" id="proxyId" required>
            </div>
            <div class="form-group">
                <label>IP Address:</label>
                <input type="text" id="ipAddress" required>
            </div>
            <button type="submit">Add IP</button>
        </form>

        <div id="ipResult"></div>

        <h2>View Whitelist</h2>
        <form id="viewWhitelistForm">
            <div class="form-group">
                <label>Proxy ID:</label>
                <input type="text" id="viewProxyId" required>
            </div>
            <button type="submit">View Whitelist</button>
        </form>

        <div id="whitelistResult"></div>
    </div>

    <script>
        document.getElementById('addSubscriptionForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData();
            formData.append('action', 'add_subscription');
            formData.append('password', document.getElementById('password').value);
            formData.append('original_url', document.getElementById('originalUrl').value);
            
            try {
                const response = await fetch('/admin', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('subscriptionResult').innerHTML = \`
                        <div class="result success">
                            <h3>Proxy Created Successfully!</h3>
                            <p><strong>Proxy URL:</strong> <a href="\${result.proxy_url}" target="_blank">\${result.proxy_url}</a></p>
                            <p><strong>Whitelist URL:</strong> <a href="\${result.whitelist_url}" target="_blank">\${result.whitelist_url}</a></p>
                            <p><strong>Proxy ID:</strong> \${result.proxy_id}</p>
                        </div>
                    \`;
                } else {
                    document.getElementById('subscriptionResult').innerHTML = \`
                        <div class="result error">Error creating proxy</div>
                    \`;
                }
            } catch (error) {
                document.getElementById('subscriptionResult').innerHTML = \`
                    <div class="result error">Error: \${error.message}</div>
                \`;
            }
        });
        
        document.getElementById('addIPForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData();
            formData.append('action', 'add_ip');
            formData.append('password', document.getElementById('ipPassword').value);
            formData.append('proxy_id', document.getElementById('proxyId').value);
            formData.append('ip', document.getElementById('ipAddress').value);

            try {
                const response = await fetch('/admin', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('ipResult').innerHTML = \`
                        <div class="result success">
                            IP added to whitelist successfully!<br>
                            IP: \${result.ip}<br>
                            Proxy ID: \${result.proxy_id}
                        </div>
                    \`;
                } else {
                    document.getElementById('ipResult').innerHTML = \`
                        <div class="result error">Error: \${result.error || 'Unknown error'}</div>
                    \`;
                }
            } catch (error) {
                document.getElementById('ipResult').innerHTML = \`
                    <div class="result error">Error: \${error.message}</div>
                \`;
            }
        });

        document.getElementById('viewWhitelistForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const proxyId = document.getElementById('viewProxyId').value;

            try {
                const response = await fetch(\`/api/whitelist/\${proxyId}\`, {
                    method: 'GET'
                });

                const result = await response.json();

                if (result.error) {
                    document.getElementById('whitelistResult').innerHTML = \`
                        <div class="result error">Error: \${result.error}</div>
                    \`;
                } else {
                    document.getElementById('whitelistResult').innerHTML = \`
                        <div class="result success">
                            <h3>Whitelist for Proxy ID: \${result.proxy_id}</h3>
                            <p><strong>Your IP:</strong> \${result.ip}</p>
                            <p><strong>Whitelisted:</strong> \${result.whitelisted ? 'Yes' : 'No'}</p>
                            <p><strong>Total IPs:</strong> \${result.total_ips}</p>
                            <p><strong>All Whitelisted IPs:</strong></p>
                            <ul>
                                \${result.all_ips.map(ip => \`<li>\${ip}</li>\`).join('')}
                            </ul>
                        </div>
                    \`;
                }
            } catch (error) {
                document.getElementById('whitelistResult').innerHTML = \`
                    <div class="result error">Error: \${error.message}</div>
                \`;
            }
        });
    </script>
</body>
</html>`;
}

// 获取白名单助手页面HTML
function getWhitelistHelperHTML() {
  return `<!DOCTYPE html>
<html>
<head>
    <title>Whitelist Helper</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin: 20px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 100%; padding: 8px; margin-bottom: 10px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; cursor: pointer; margin: 5px; }
        button:hover { background: #005a87; }
        .result { background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .info { background: #e3f2fd; color: #1976d2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Whitelist Helper</h1>
        <p>Use this page to add your IP to the whitelist or check your whitelist status.</p>

        <div class="form-group">
            <label>Proxy ID:</label>
            <input type="text" id="proxyId" placeholder="Enter your proxy ID" required>
        </div>

        <button onclick="addCurrentIP()">Add My IP to Whitelist</button>
        <button onclick="checkWhitelistStatus()">Check Whitelist Status</button>

        <div id="result"></div>

        <div class="info">
            <h3>Instructions:</h3>
            <ul>
                <li>Enter your Proxy ID in the field above</li>
                <li>Click "Add My IP to Whitelist" to automatically add your current IP</li>
                <li>Click "Check Whitelist Status" to see if your IP is whitelisted</li>
            </ul>
        </div>
    </div>

    <script>
        async function addCurrentIP() {
            const proxyId = document.getElementById('proxyId').value;

            if (!proxyId) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">Please enter a Proxy ID</div>
                \`;
                return;
            }

            try {
                const response = await fetch(\`/api/whitelist/\${proxyId}\`, {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('result').innerHTML = \`
                        <div class="result success">
                            <h3>Success!</h3>
                            <p><strong>Message:</strong> \${result.message}</p>
                            <p><strong>Your IP:</strong> \${result.ip}</p>
                            <p><strong>Proxy ID:</strong> \${result.proxy_id}</p>
                            <p><strong>Total IPs in whitelist:</strong> \${result.total_ips}</p>
                        </div>
                    \`;
                } else {
                    document.getElementById('result').innerHTML = \`
                        <div class="result error">
                            <h3>Error</h3>
                            <p>\${result.error}</p>
                        </div>
                    \`;
                }
            } catch (error) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">
                        <h3>Error</h3>
                        <p>Failed to add IP: \${error.message}</p>
                    </div>
                \`;
            }
        }

        async function checkWhitelistStatus() {
            const proxyId = document.getElementById('proxyId').value;

            if (!proxyId) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">Please enter a Proxy ID</div>
                \`;
                return;
            }

            try {
                const response = await fetch(\`/api/whitelist/\${proxyId}\`, {
                    method: 'GET'
                });

                const result = await response.json();

                if (result.error) {
                    document.getElementById('result').innerHTML = \`
                        <div class="result error">
                            <h3>Error</h3>
                            <p>\${result.error}</p>
                        </div>
                    \`;
                } else {
                    const statusClass = result.whitelisted ? 'success' : 'error';
                    const statusText = result.whitelisted ? 'Whitelisted' : 'Not Whitelisted';

                    document.getElementById('result').innerHTML = \`
                        <div class="result \${statusClass}">
                            <h3>Whitelist Status</h3>
                            <p><strong>Status:</strong> \${statusText}</p>
                            <p><strong>Your IP:</strong> \${result.ip}</p>
                            <p><strong>Proxy ID:</strong> \${result.proxy_id}</p>
                            <p><strong>Total IPs in whitelist:</strong> \${result.total_ips}</p>
                            <p><strong>All whitelisted IPs:</strong></p>
                            <ul>
                                \${result.all_ips.map(ip => \`<li>\${ip}</li>\`).join('')}
                            </ul>
                        </div>
                    \`;
                }
            } catch (error) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">
                        <h3>Error</h3>
                        <p>Failed to check status: \${error.message}</p>
                    </div>
                \`;
            }
        }
    </script>
</body>
</html>`;
}
