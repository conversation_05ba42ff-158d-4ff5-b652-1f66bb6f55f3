<!DOCTYPE html>
<html>
<head>
    <title>Test Whitelist</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 600px; margin: 0 auto; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; cursor: pointer; margin: 5px; }
        .result { background: #f0f0f0; padding: 15px; margin: 20px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Whitelist for Proxy ID: 4yqj96pexcyb4oh8bo0psg</h1>
        
        <button onclick="addIP()">Add My IP to Whitelist (POST)</button>
        <button onclick="checkStatus()">Check Whitelist Status (GET)</button>
        
        <div id="result"></div>
    </div>

    <script>
        const proxyId = '4yqj96pexcyb4oh8bo0psg';
        const baseUrl = 'https://cf-subscription-proxy.huang9832.workers.dev'; // 替换为你的实际URL
        
        async function addIP() {
            try {
                const response = await fetch(`${baseUrl}/api/whitelist/${proxyId}`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                document.getElementById('result').innerHTML = `
                    <div class="result">
                        <h3>Add IP Result (POST)</h3>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <div class="result">
                        <h3>Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function checkStatus() {
            try {
                const response = await fetch(`${baseUrl}/api/whitelist/${proxyId}`, {
                    method: 'GET'
                });
                
                const result = await response.json();
                
                document.getElementById('result').innerHTML = `
                    <div class="result">
                        <h3>Check Status Result (GET)</h3>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <div class="result">
                        <h3>Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
